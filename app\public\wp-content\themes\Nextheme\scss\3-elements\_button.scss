
/*=============================================
=            Button            =
=============================================*/

.default-btn {
    font-size: 20px;
    font-weight: 700;
    line-height: 1;

    display: inline-block;

    padding: 15px 20px;

    color: $white; // White text on accent button
    border: none;
    background: none;
    background: $theme-color--accent; // Sage Green for CTA buttons

    &--hero-slider {
        font-size: 28px;

        padding: 17px 25px;
        @media #{$tablet-device} {
            font-size: 25px;

            padding: 15px 20px;
        }
        @media #{$large-mobile} {
            font-size: 22px;

            padding: 15px 20px;
        }
    }

    &:hover {
        color: $theme-color--light-text; // Light text on hover
        background: $theme-color--default; // Slate Blue on hover
    }
}

.see-more-link {
    font-size: 18px;
    font-weight: 700;
    line-height: 1;

    transition: $transition--default;
    text-decoration: underline;

    color: $theme-color--black;
    &:hover {
        text-decoration: underline;

        color: $theme-color--default;
    }

    &--color {
        color: $theme-color--default;
        &:hover {
            color: $theme-color--black;
        }
    }
}

/*=====  End of Button  ======*/

