/*=============================================
=            backgrounds            =
=============================================*/

.background-repeat {
    &--x {
        background-repeat: repeat-x;
    }
    &--x-bottom {
        background-repeat: repeat-x;
        background-position: bottom;
    }
}

.background-color {
    &--dark {
        background-color: $theme-color--default; // Slate Blue for dark sections
    }
    &--deep-dark {
        background-color: darken($theme-color--default, 15%); // Darker Slate Blue
    }
    &--dark-overlay {
        background-color: rgba(83, 104, 120, 0.9); // Slate Blue overlay
    }
    &--default {
        background-color: $theme-color--default; // Primary Slate Blue
    }
    &--default-overlay {
        background-color: rgba(83, 104, 120, 0.9); // Slate Blue overlay
    }
    &--default-light-overlay {
        background-color: rgba(83, 104, 120, 0.8); // Light Slate Blue overlay
    }
    &--light {
        background-color: $theme-color--light-bg; // Light Gray background
    }
    &--accent {
        background-color: $theme-color--accent; // Sage Green accent background
    }
}

/*=====  End of backgrounds  ======*/